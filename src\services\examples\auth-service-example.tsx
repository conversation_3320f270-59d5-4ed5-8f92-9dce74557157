import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z as zod } from 'zod';

import {
  <PERSON>,
  Card,
  Stack,
  Button,
  TextField,
  Typography,
  Alert,
  Divider,
} from '@mui/material';

import { useAuthService } from '../hooks/use-auth-service';

// Schemas
const SignInSchema = zod.object({
  email: zod.string().email('Invalid email address'),
  password: zod.string().min(6, 'Password must be at least 6 characters'),
});

const SignUpSchema = zod.object({
  name: zod.string().min(1, 'Name is required'),
  username: zod.string().min(1, 'Username is required'),
  email: zod.string().email('Invalid email address'),
  password: zod.string().min(6, 'Password must be at least 6 characters'),
});

type SignInFormData = zod.infer<typeof SignInSchema>;
type SignUpFormData = zod.infer<typeof SignUpSchema>;

/**
 * Example component showing how to use the useAuthService hook
 * This demonstrates both sign-in and sign-up functionality
 */
export function AuthServiceExample() {
  const [mode, setMode] = useState<'signin' | 'signup'>('signin');
  
  // Use the authentication service hook
  const {
    signInAsync,
    signUpAsync,
    isSigningIn,
    isSigningUp,
    signInError,
    signUpError,
    resetSignIn,
    resetSignUp,
  } = useAuthService();

  // Sign In Form
  const signInForm = useForm<SignInFormData>({
    resolver: zodResolver(SignInSchema),
    defaultValues: {
      email: '<EMAIL>',
      password: '<EMAIL>',
    },
  });

  // Sign Up Form
  const signUpForm = useForm<SignUpFormData>({
    resolver: zodResolver(SignUpSchema),
    defaultValues: {
      name: 'test123',
      username: 'test123',
      email: '<EMAIL>',
      password: '<EMAIL>',
    },
  });

  // Handle Sign In
  const handleSignIn = async (data: SignInFormData) => {
    try {
      const result = await signInAsync(data);
      console.log('Sign in successful:', result);
      // Handle successful sign in (e.g., redirect to dashboard)
    } catch (error) {
      console.error('Sign in failed:', error);
      // Error is already handled by the hook with snackbar
    }
  };

  // Handle Sign Up
  const handleSignUp = async (data: SignUpFormData) => {
    try {
      const result = await signUpAsync(data);
      console.log('Sign up successful:', result);
      // Handle successful sign up (e.g., redirect to dashboard)
    } catch (error) {
      console.error('Sign up failed:', error);
      // Error is already handled by the hook with snackbar
    }
  };

  return (
    <Box sx={{ maxWidth: 500, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Authentication Service Example
      </Typography>

      {/* Mode Toggle */}
      <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
        <Button
          variant={mode === 'signin' ? 'contained' : 'outlined'}
          onClick={() => {
            setMode('signin');
            resetSignIn();
          }}
        >
          Sign In
        </Button>
        <Button
          variant={mode === 'signup' ? 'contained' : 'outlined'}
          onClick={() => {
            setMode('signup');
            resetSignUp();
          }}
        >
          Sign Up
        </Button>
      </Stack>

      <Card sx={{ p: 3 }}>
        {mode === 'signin' ? (
          // Sign In Form
          <form onSubmit={signInForm.handleSubmit(handleSignIn)}>
            <Stack spacing={3}>
              <Typography variant="h6">Sign In</Typography>
              
              {signInError && (
                <Alert severity="error">
                  {signInError.message || 'Sign in failed'}
                </Alert>
              )}

              <TextField
                {...signInForm.register('email')}
                label="Email"
                type="email"
                error={!!signInForm.formState.errors.email}
                helperText={signInForm.formState.errors.email?.message}
                fullWidth
              />

              <TextField
                {...signInForm.register('password')}
                label="Password"
                type="password"
                error={!!signInForm.formState.errors.password}
                helperText={signInForm.formState.errors.password?.message}
                fullWidth
              />

              <Button
                type="submit"
                variant="contained"
                size="large"
                disabled={isSigningIn}
                fullWidth
              >
                {isSigningIn ? 'Signing In...' : 'Sign In'}
              </Button>
            </Stack>
          </form>
        ) : (
          // Sign Up Form
          <form onSubmit={signUpForm.handleSubmit(handleSignUp)}>
            <Stack spacing={3}>
              <Typography variant="h6">Sign Up</Typography>
              
              {signUpError && (
                <Alert severity="error">
                  {signUpError.message || 'Sign up failed'}
                </Alert>
              )}

              <TextField
                {...signUpForm.register('name')}
                label="Full Name"
                error={!!signUpForm.formState.errors.name}
                helperText={signUpForm.formState.errors.name?.message}
                fullWidth
              />

              <TextField
                {...signUpForm.register('username')}
                label="Username"
                error={!!signUpForm.formState.errors.username}
                helperText={signUpForm.formState.errors.username?.message}
                fullWidth
              />

              <TextField
                {...signUpForm.register('email')}
                label="Email"
                type="email"
                error={!!signUpForm.formState.errors.email}
                helperText={signUpForm.formState.errors.email?.message}
                fullWidth
              />

              <TextField
                {...signUpForm.register('password')}
                label="Password"
                type="password"
                error={!!signUpForm.formState.errors.password}
                helperText={signUpForm.formState.errors.password?.message}
                fullWidth
              />

              <Button
                type="submit"
                variant="contained"
                size="large"
                disabled={isSigningUp}
                fullWidth
              >
                {isSigningUp ? 'Creating Account...' : 'Create Account'}
              </Button>
            </Stack>
          </form>
        )}
      </Card>

      <Divider sx={{ my: 3 }} />

      <Typography variant="body2" color="text.secondary">
        <strong>API Endpoints:</strong>
        <br />
        • Sign In: POST /auth/login/basic
        <br />
        • Sign Up: POST /auth/register
        <br />
        <br />
        <strong>Usage:</strong>
        <br />
        Import and use the useAuthService hook in your components to handle authentication.
      </Typography>
    </Box>
  );
}
