import { useMutation } from '@tanstack/react-query';
import type { AxiosError } from 'axios';

import axiosInstance from 'src/utils/axios';
import { useApiResult } from './use-api-result';

// Types for authentication
export interface SignInCredentials {
  email: string;
  password: string;
}

export interface SignUpCredentials {
  name: string;
  username: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  accessToken: string;
  user?: {
    id: string;
    name: string;
    username: string;
    email: string;
  };
}

// Custom hook for authentication services
export const useAuthService = () => {
  const { handleApiSuccessWithSnackbar, handleApiErrorWithSnackbar } = useApiResult();

  // Sign In Mutation
  const signInMutation = useMutation<AuthResponse, AxiosError, SignInCredentials>({
    mutationFn: async (credentials) => {
      const response = await axiosInstance.post('/auth/login/basic', credentials);
      return response.data;
    },
    onSuccess: (data) => {
      // Store token in session storage
      if (data.accessToken) {
        sessionStorage.setItem('accessToken', data.accessToken);
      }
      handleApiSuccessWithSnackbar('Successfully signed in!');
    },
    onError: (error) => {
      handleApiErrorWithSnackbar(error);
    },
  });

  // Sign Up Mutation
  const signUpMutation = useMutation<AuthResponse, AxiosError, SignUpCredentials>({
    mutationFn: async (credentials) => {
      const response = await axiosInstance.post('/auth/register', credentials);
      return response.data;
    },
    onSuccess: (data) => {
      // Store token in session storage
      if (data.accessToken) {
        sessionStorage.setItem('accessToken', data.accessToken);
      }
      handleApiSuccessWithSnackbar('Account created successfully!');
    },
    onError: (error) => {
      handleApiErrorWithSnackbar(error);
    },
  });

  return {
    // Sign In
    signIn: signInMutation.mutate,
    signInAsync: signInMutation.mutateAsync,
    isSigningIn: signInMutation.isPending,
    signInError: signInMutation.error,
    signInData: signInMutation.data,

    // Sign Up
    signUp: signUpMutation.mutate,
    signUpAsync: signUpMutation.mutateAsync,
    isSigningUp: signUpMutation.isPending,
    signUpError: signUpMutation.error,
    signUpData: signUpMutation.data,

    // Reset mutations
    resetSignIn: signInMutation.reset,
    resetSignUp: signUpMutation.reset,
  };
};
