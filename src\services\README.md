# Authentication Service

This directory contains custom authentication services and hooks that connect to your specific API endpoints.

## Overview

The authentication service provides a clean interface for handling user authentication with your backend API endpoints:

- **Sign In**: `POST /auth/login/basic`
- **Sign Up**: `POST /auth/register`

## Files Structure

```
src/services/
├── hooks/
│   ├── use-auth-service.ts     # Main authentication hook
│   ├── use-api-services.ts     # Generic API services
│   └── use-api-result.ts       # API result handling
├── examples/
│   └── auth-service-example.tsx # Example usage component
└── README.md                   # This file
```

## Usage

### 1. Import the Hook

```typescript
import { useAuthService } from 'src/services/hooks/use-auth-service';
```

### 2. Use in Your Component

```typescript
export function MyAuthComponent() {
  const {
    signInAsync,
    signUpAsync,
    isSigningIn,
    isSigningUp,
    signInError,
    signUpError,
  } = useAuthService();

  const handleSignIn = async () => {
    try {
      const result = await signInAsync({
        email: '<EMAIL>',
        password: '<EMAIL>'
      });
      console.log('Success:', result);
    } catch (error) {
      console.error('Failed:', error);
    }
  };

  const handleSignUp = async () => {
    try {
      const result = await signUpAsync({
        name: 'test123',
        username: 'test123',
        email: '<EMAIL>',
        password: '<EMAIL>'
      });
      console.log('Success:', result);
    } catch (error) {
      console.error('Failed:', error);
    }
  };

  return (
    <div>
      <button onClick={handleSignIn} disabled={isSigningIn}>
        {isSigningIn ? 'Signing In...' : 'Sign In'}
      </button>
      <button onClick={handleSignUp} disabled={isSigningUp}>
        {isSigningUp ? 'Creating Account...' : 'Sign Up'}
      </button>
    </div>
  );
}
```

## API Data Formats

### Sign In Request
```json
{
  "email": "<EMAIL>",
  "password": "<EMAIL>"
}
```

### Sign Up Request
```json
{
  "name": "test123",
  "username": "test123", 
  "email": "<EMAIL>",
  "password": "<EMAIL>"
}
```

### Expected Response
```json
{
  "accessToken": "your-jwt-token-here",
  "user": {
    "id": "user-id",
    "name": "User Name",
    "username": "username",
    "email": "<EMAIL>"
  }
}
```

## Hook API Reference

### Returns

- `signIn(credentials)` - Trigger sign in mutation
- `signInAsync(credentials)` - Async sign in with promise
- `isSigningIn` - Boolean loading state for sign in
- `signInError` - Error object if sign in fails
- `signInData` - Response data from successful sign in

- `signUp(credentials)` - Trigger sign up mutation
- `signUpAsync(credentials)` - Async sign up with promise
- `isSigningUp` - Boolean loading state for sign up
- `signUpError` - Error object if sign up fails
- `signUpData` - Response data from successful sign up

- `resetSignIn()` - Reset sign in mutation state
- `resetSignUp()` - Reset sign up mutation state

## Features

- ✅ Automatic error handling with snackbar notifications
- ✅ Loading states for UI feedback
- ✅ Token storage in sessionStorage
- ✅ TypeScript support with proper types
- ✅ React Query integration for caching and state management
- ✅ Consistent API interface

## Integration with Existing Auth System

The service is designed to work alongside your existing JWT authentication system. The endpoints have been updated in `src/utils/axios.ts` to match your API:

```typescript
auth: {
  me: '/api/auth/me',
  signIn: '/auth/login/basic',    // Updated
  signUp: '/auth/register',       // Updated
  resetPassword: '/api/auth/reset-password',
}
```

## Example Component

See `src/services/examples/auth-service-example.tsx` for a complete working example with forms and error handling.
